
import React, { useState, useEffect } from 'react';
import FileUpload from '@/components/FileUpload';
import SummaryDisplay from '@/components/SummaryDisplay';
import { generateSummary } from '@/utils/textProcessor';

interface Summary {
  summary: string;
  keyPoints: string[];
  fileName?: string;
}

const Index = () => {
  const [currentSummary, setCurrentSummary] = useState<Summary | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const [progressiveText, setProgressiveText] = useState('');
  const [progressivePoints, setProgressivePoints] = useState<string[]>([]);

  const simulateProgressiveRendering = (summary: Summary) => {
    setProgressiveText('');
    setProgressivePoints([]);
    
    // Animate summary text
    let currentIndex = 0;
    const summaryInterval = setInterval(() => {
      if (currentIndex < summary.summary.length) {
        setProgressiveText(summary.summary.slice(0, currentIndex + 1));
        currentIndex++;
      } else {
        clearInterval(summaryInterval);
        
        // Start animating key points
        let pointIndex = 0;
        const pointsInterval = setInterval(() => {
          if (pointIndex < summary.keyPoints.length) {
            setProgressivePoints(prev => [...prev, summary.keyPoints[pointIndex]]);
            pointIndex++;
          } else {
            clearInterval(pointsInterval);
            // Animation complete, set final summary
            setTimeout(() => {
              setCurrentSummary(summary);
              setIsGenerating(false);
            }, 500);
          }
        }, 300);
      }
    }, 20);
  };

  const handleFileUpload = async (content: string, fileName?: string) => {
    setIsGenerating(true);
    setCurrentSummary(null);

    // Simulate processing time then start progressive rendering
    setTimeout(() => {
      const result = generateSummary(content);
      const summaryWithFile = {
        ...result,
        fileName
      };
      simulateProgressiveRendering(summaryWithFile);
    }, 1000);
  };

  const handleTextSubmit = async (text: string) => {
    setIsGenerating(true);
    setCurrentSummary(null);

    // Simulate processing time then start progressive rendering
    setTimeout(() => {
      const result = generateSummary(text);
      simulateProgressiveRendering(result);
    }, 1000);
  };

  const handleNewDocument = () => {
    setCurrentSummary(null);
    setProgressiveText('');
    setProgressivePoints([]);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header with Kintess in top left */}
      <header className="border-b border-gray-100 bg-white/80 backdrop-blur-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4 max-w-7xl flex justify-between items-center">
          <h1 className="text-2xl font-bold text-black tracking-tight hover:text-gray-700 transition-colors cursor-default">
            Kintess
          </h1>

        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Hero Section */}
        {!currentSummary && !isGenerating && (
          <div className="text-center mb-12 animate-fade-in">
            <div className="max-w-3xl mx-auto space-y-6">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-black mb-6 leading-tight">
                Document Summarisation
                <span className="block text-gray-600 mt-2">Made Simple</span>
              </h2>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="space-y-8">
          {!currentSummary && !isGenerating && (
            <div className="animate-fade-in">
              <FileUpload
                onFileUpload={handleFileUpload}
                onTextSubmit={handleTextSubmit}
                disabled={false}
              />
            </div>
          )}

          {isGenerating && (
            <div className="space-y-8 animate-fade-in">
              <div className="text-center py-8">
                <div className="inline-flex items-center space-x-4 bg-gray-50 px-8 py-6 rounded-2xl border border-gray-100">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-black rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
                    <div className="w-2 h-2 bg-black rounded-full animate-bounce" style={{animationDelay: '150ms'}}></div>
                    <div className="w-2 h-2 bg-black rounded-full animate-bounce" style={{animationDelay: '300ms'}}></div>
                  </div>
                  <div>
                    <span className="text-lg font-medium text-gray-900 block">
                      Generating summary...
                    </span>
                    <p className="text-sm text-gray-500 mt-1">
                      This may take a few moments
                    </p>
                  </div>
                </div>
              </div>

              {/* Progressive Summary Rendering */}
              {(progressiveText || progressivePoints.length > 0) && (
                <div className="w-full max-w-3xl mx-auto">
                  <div className="bg-white border border-gray-100 rounded-2xl p-8 shadow-sm">
                    <div className="space-y-6">
                      {/* Progressive Summary */}
                      {progressiveText && (
                        <div className="space-y-4">
                          <h2 className="text-2xl font-bold text-gray-900 pb-3 border-b border-gray-100">
                            Summary
                          </h2>
                          <div className="prose prose-gray max-w-none">
                            <p className="text-gray-700 leading-relaxed text-lg">
                              {progressiveText}
                              <span className="animate-pulse">|</span>
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Progressive Key Points */}
                      {progressivePoints.length > 0 && (
                        <div className="space-y-4">
                          <h3 className="text-xl font-bold text-gray-900 pb-3 border-b border-gray-100">
                            Key Points
                          </h3>
                          <ul className="space-y-3">
                            {progressivePoints.map((point, index) => (
                              <li key={index} className="flex items-start space-x-3 animate-fade-in">
                                <span className="flex-shrink-0 w-2 h-2 bg-gray-400 rounded-full mt-3"></span>
                                <span className="text-gray-700 leading-relaxed">{point}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {currentSummary && (
            <div className="space-y-8 animate-fade-in">
              <SummaryDisplay
                summary={currentSummary.summary}
                keyPoints={currentSummary.keyPoints}
                fileName={currentSummary.fileName}
              />
              
              <div className="text-center">
                <button
                  onClick={handleNewDocument}
                  className="inline-flex items-center text-black font-medium px-6 py-3 rounded-lg border border-gray-200 hover:border-black hover:bg-gray-50 transition-all duration-200 group"
                >
                  <span className="group-hover:translate-x-0.5 transition-transform duration-200">
                    Summarise another document
                  </span>
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer with Privacy Notice */}
        <footer className="mt-20 pt-8 border-t border-gray-100 text-center space-y-3">
          <p className="text-sm text-gray-600 font-medium">
            🔒 All data is processed in-session. We don't store your files or summaries.
          </p>
          <p className="text-sm text-gray-400">
            Kintess - Document Summarisation Made Simple
          </p>
        </footer>
      </div>
    </div>
  );
};

export default Index;
