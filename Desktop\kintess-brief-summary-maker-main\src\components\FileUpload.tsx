
import React, { useState, useRef } from 'react';
import { Upload, FileText, File, Sparkles, Lightbulb, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface FileUploadProps {
  onFileUpload: (content: string, fileName?: string) => void;
  onTextSubmit: (text: string) => void;
  disabled?: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFileUpload, onTextSubmit, disabled = false }) => {
  const [dragActive, setDragActive] = useState(false);
  const [textContent, setTextContent] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [fileSizeError, setFileSizeError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB

  const validateFileSize = (file: File): boolean => {
    if (file.size > MAX_FILE_SIZE) {
      setFileSizeError(`File "${file.name}" is too large. Maximum file size is 20MB.`);
      return false;
    }
    setFileSizeError('');
    return true;
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (disabled) return;
    
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (disabled || !e.dataTransfer.files || !e.dataTransfer.files[0]) return;
    
    handleFileUpload(e.dataTransfer.files[0]);
  };

  const handleFileUpload = async (file: File) => {
    if (disabled) return;
    
    const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
    
    if (!allowedTypes.includes(file.type)) {
      setFileSizeError('Please upload a PDF, DOCX, or TXT file.');
      return;
    }

    if (!validateFileSize(file)) return;

    setIsProcessing(true);
    setUploadedFile(file);
    try {
      const text = await file.text();
      onFileUpload(text, file.name);
    } catch (error) {
      console.error('Error reading file:', error);
      setFileSizeError('Error reading file. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const onButtonClick = () => {
    if (disabled) return;
    fileInputRef.current?.click();
  };

  const handleTextSubmit = () => {
    if (disabled || !textContent.trim()) return;
    onTextSubmit(textContent.trim());
    setTextContent('');
  };

  const removeFile = () => {
    setUploadedFile(null);
    setFileSizeError('');
  };

  return (
    <div className="w-full max-w-3xl mx-auto space-y-6">
      {/* Error Alert */}
      {fileSizeError && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {fileSizeError}
          </AlertDescription>
        </Alert>
      )}

      {/* File Upload Section - ChatGPT Style */}
      <div className="space-y-4">
        {/* Uploaded File Display */}
        {uploadedFile && (
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl border border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center border border-gray-200">
                <File className="w-4 h-4 text-gray-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">{uploadedFile.name}</p>
                <p className="text-xs text-gray-500">{(uploadedFile.size / 1024).toFixed(1)} KB</p>
              </div>
            </div>
            <button
              onClick={removeFile}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              ×
            </button>
          </div>
        )}

        {/* Upload Area */}
        {!uploadedFile && (
          <div
            className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 ${
              disabled 
                ? 'border-gray-100 bg-gray-50 cursor-not-allowed opacity-60'
                : dragActive 
                  ? 'border-gray-400 bg-gray-50' 
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50/50 cursor-pointer'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={onButtonClick}
          >
            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              accept=".pdf,.docx,.txt"
              onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
              disabled={disabled}
            />
            
            <Upload className={`w-8 h-8 mx-auto mb-3 transition-colors ${
              disabled ? 'text-gray-300' : 'text-gray-400'
            }`} />
            <p className="text-sm font-medium text-gray-900 mb-1">
              {disabled ? 'Upload disabled' : dragActive ? 'Drop your document here' : 'Upload document'}
            </p>
            {!disabled && (
              <p className="text-xs text-gray-500 mb-3">
                Drag and drop or <span className="text-gray-900 underline">browse files</span>
              </p>
            )}
            <div className="flex items-center justify-center space-x-3 text-xs text-gray-400">
              <span>PDF</span>
              <span>•</span>
              <span>DOCX</span>
              <span>•</span>
              <span>TXT</span>
              <span>•</span>
              <span>Max 20MB</span>
            </div>
          </div>
        )}

        {/* Smart Prompt Examples */}
        {!disabled && (
          <div className="bg-gray-50 rounded-xl p-4 border border-gray-100">
            <div className="flex items-start space-x-3">
              <Lightbulb className="w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-gray-600">
                Try uploading a contract, research paper, or long report to see how Kintess works.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-200"></div>
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="bg-white px-4 text-gray-500 font-medium">OR</span>
        </div>
      </div>

      {/* Text Input Section - ChatGPT Style */}
      <div className="space-y-4">
        <div className="relative">
          <Textarea
            placeholder={disabled ? "No summaries remaining" : "Type or paste your text here for summarisation..."}
            value={textContent}
            onChange={(e) => setTextContent(e.target.value)}
            disabled={disabled}
            className="min-h-32 resize-none border-gray-200 focus:border-black focus:ring-1 focus:ring-black rounded-xl text-base leading-relaxed disabled:bg-gray-50 disabled:text-gray-400 pr-12"
          />
          <Button
            onClick={handleTextSubmit}
            disabled={!textContent.trim() || isProcessing || disabled}
            className="absolute bottom-3 right-3 h-8 w-8 p-0 bg-black text-white hover:bg-gray-800 rounded-lg transition-all duration-200 disabled:opacity-50"
          >
            {isProcessing ? (
              <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent"></div>
            ) : (
              <Sparkles className="w-4 h-4" />
            )}
          </Button>
        </div>

        {/* Character count and file size info */}
        <div className="flex justify-between items-center text-xs text-gray-500">
          <span>Max file size: 20MB</span>
          <span>{textContent.length} characters</span>
        </div>

        {/* Subheading moved here - below the textbox */}
        <div className="text-center">
          <p className="text-gray-500 text-sm">
            Kintess helps you turn long documents into clear, concise summaries in seconds.
          </p>
        </div>
      </div>
    </div>
  );
};

export default FileUpload;
